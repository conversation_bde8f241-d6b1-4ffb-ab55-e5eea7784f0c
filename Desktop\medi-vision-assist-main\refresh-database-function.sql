-- Refresh the search_medicines function
-- Run this in Supabase SQL Editor to ensure the function is properly registered

-- Enable required extensions for similarity search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;

-- First, drop the function if it exists
DROP FUNCTION IF EXISTS public.search_medicines(TEXT, INTEGER, REAL);

-- Recreate the function with proper permissions
CREATE OR REPLACE FUNCTION public.search_medicines(
    search_term TEXT,
    limit_count INTEGER DEFAULT 10,
    similarity_threshold REAL DEFAULT 0.3
)
RETURNS TABLE (
    id UUID,
    generic_name TEXT,
    brand_names TEXT[],
    medicine_type TEXT,
    strength TEXT,
    indications TEXT[],
    similarity_score REAL,
    search_rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.generic_name,
        m.brand_names,
        m.medicine_type,
        m.strength,
        m.indications,
        GREATEST(
            similarity(m.generic_name, search_term),
            COALESCE((
                SELECT MAX(similarity(brand_name, search_term))
                FROM unnest(m.brand_names) AS brand_name
            ), 0),
            COALESCE((
                SELECT MAX(similarity(alt_name, search_term))
                FROM unnest(m.alternative_names) AS alt_name
            ), 0)
        ) as similarity_score,
        ts_rank(m.search_vector, plainto_tsquery('english', search_term)) as search_rank
    FROM public.medicines m
    WHERE 
        -- Text similarity search
        (
            similarity(m.generic_name, search_term) > similarity_threshold OR
            EXISTS (
                SELECT 1 FROM unnest(m.brand_names) AS brand_name
                WHERE similarity(brand_name, search_term) > similarity_threshold
            ) OR
            EXISTS (
                SELECT 1 FROM unnest(m.alternative_names) AS alt_name
                WHERE similarity(alt_name, search_term) > similarity_threshold
            )
        )
        OR
        -- Full-text search
        m.search_vector @@ plainto_tsquery('english', search_term)
    ORDER BY 
        similarity_score DESC,
        search_rank DESC,
        m.popularity_score DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.search_medicines TO anon, authenticated;

-- Test the function
SELECT * FROM public.search_medicines('acetaminophen', 5, 0.3);
