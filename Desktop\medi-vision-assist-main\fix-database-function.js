/**
 * Fix missing search_medicines function in Supabase database
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://ygkxdctaraeragizxfbt.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlna3hkY3RhcmFlcmFnaXp4ZmJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNDU3MTksImV4cCI6MjA2NTgyMTcxOX0.XgG9sd78RISddxuGIIJoFyBiPW4f8ynj-sQCteDpRcc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDatabaseFunction() {
  console.log('🔧 Fixing database function...');
  
  try {
    // First, test if the function exists
    console.log('1. Testing if search_medicines function exists...');
    const { data: testResult, error: testError } = await supabase
      .rpc('search_medicines', {
        search_term: 'test',
        limit_count: 1,
        similarity_threshold: 0.3
      });
    
    if (!testError) {
      console.log('✅ Function already exists and works!');
      return true;
    }
    
    console.log('❌ Function missing or broken:', testError.message);
    
    // Check if medicines table exists
    console.log('2. Checking if medicines table exists...');
    const { data: tableCheck, error: tableError } = await supabase
      .from('medicines')
      .select('id')
      .limit(1);
    
    if (tableError) {
      console.log('❌ Medicines table missing:', tableError.message);
      console.log('🔧 Need to create medicines table first...');
      
      // Try to create a basic medicines table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.medicines (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          generic_name TEXT NOT NULL,
          brand_names TEXT[] DEFAULT '{}',
          alternative_names TEXT[] DEFAULT '{}',
          medicine_type TEXT,
          strength TEXT,
          indications TEXT[] DEFAULT '{}',
          popularity_score INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      console.log('Creating medicines table...');
      // Note: This might fail with anon key, but let's try
      const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
      
      if (createError) {
        console.log('❌ Cannot create table with current permissions:', createError.message);
        console.log('💡 You need to run the migration manually in Supabase dashboard');
        return false;
      }
    } else {
      console.log('✅ Medicines table exists');
    }
    
    // Try to create the search function (this will likely fail with anon key)
    console.log('3. Attempting to create search_medicines function...');
    
    const functionSQL = `
      CREATE OR REPLACE FUNCTION search_medicines(
          search_term TEXT,
          limit_count INTEGER DEFAULT 10,
          similarity_threshold REAL DEFAULT 0.3
      )
      RETURNS TABLE (
          id UUID,
          generic_name TEXT,
          brand_names TEXT[],
          medicine_type TEXT,
          strength TEXT,
          indications TEXT[],
          similarity_score REAL,
          search_rank REAL
      ) AS $$
      BEGIN
          RETURN QUERY
          SELECT 
              m.id,
              m.generic_name,
              m.brand_names,
              m.medicine_type,
              m.strength,
              m.indications,
              0.5::REAL as similarity_score,
              0.5::REAL as search_rank
          FROM public.medicines m
          WHERE 
              m.generic_name ILIKE '%' || search_term || '%' OR
              EXISTS (
                  SELECT 1 FROM unnest(m.brand_names) AS brand_name
                  WHERE brand_name ILIKE '%' || search_term || '%'
              ) OR
              EXISTS (
                  SELECT 1 FROM unnest(m.alternative_names) AS alt_name
                  WHERE alt_name ILIKE '%' || search_term || '%'
              )
          ORDER BY 
              m.popularity_score DESC
          LIMIT limit_count;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;
    
    const { error: functionError } = await supabase.rpc('exec_sql', { sql: functionSQL });
    
    if (functionError) {
      console.log('❌ Cannot create function with current permissions:', functionError.message);
      console.log('💡 Manual steps needed:');
      console.log('1. Go to Supabase Dashboard > SQL Editor');
      console.log('2. Run the migration file: supabase/migrations/20250806000000_create_medicines_database.sql');
      console.log('3. Or copy and paste the function SQL above');
      return false;
    }
    
    console.log('✅ Function created successfully!');
    
    // Test the function again
    const { data: finalTest, error: finalError } = await supabase
      .rpc('search_medicines', {
        search_term: 'test',
        limit_count: 1,
        similarity_threshold: 0.3
      });
    
    if (finalError) {
      console.log('❌ Function still not working:', finalError.message);
      return false;
    }
    
    console.log('✅ Function is now working!');
    return true;
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

// Run the fix
fixDatabaseFunction().then(success => {
  if (success) {
    console.log('🎉 Database function fixed successfully!');
  } else {
    console.log('⚠️ Manual intervention required. See instructions above.');
  }
  process.exit(success ? 0 : 1);
});
