/**
 * Test script to verify the search_medicines function works
 * Run this after applying the migration to Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Try to read environment variables from .env.local
let supabaseUrl, supabaseKey;

try {
  const envPath = path.join(__dirname, '.env.local');
  const envContent = fs.readFileSync(envPath, 'utf8');

  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key === 'NEXT_PUBLIC_SUPABASE_URL') {
      supabaseUrl = value;
    } else if (key === 'NEXT_PUBLIC_SUPABASE_ANON_KEY') {
      supabaseKey = value;
    }
  });
} catch (error) {
  console.error('❌ Could not read .env.local file');
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSearchFunction() {
  console.log('🧪 Testing search_medicines function...\n');

  const testTerms = [
    'tylenol',
    'acetaminophen', 
    'panadol',
    'aspirin',
    'ibuprofen',
    'advil',
    'motrin',
    'xyz123' // Should return no results
  ];

  for (const term of testTerms) {
    try {
      console.log(`🔍 Testing search for: "${term}"`);
      
      const { data, error } = await supabase.rpc('search_medicines', {
        search_term: term,
        limit_count: 3,
        similarity_threshold: 0.3
      });

      if (error) {
        console.error(`❌ Error searching for "${term}":`, error.message);
        if (error.code === 'PGRST202') {
          console.error('💡 This means the search_medicines function does not exist in your database.');
          console.error('   Please run the SQL from apply-migration.sql in your Supabase SQL Editor.');
        }
        continue;
      }

      if (data && data.length > 0) {
        console.log(`✅ Found ${data.length} results for "${term}":`);
        data.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.generic_name} (similarity: ${result.similarity_score?.toFixed(2)})`);
          if (result.brand_names && result.brand_names.length > 0) {
            console.log(`      Brand names: ${result.brand_names.join(', ')}`);
          }
        });
      } else {
        console.log(`⚠️ No results found for "${term}"`);
      }
      
      console.log(''); // Empty line for readability
    } catch (error) {
      console.error(`❌ Unexpected error testing "${term}":`, error);
    }
  }

  console.log('🏁 Test completed!');
}

// Run the test
testSearchFunction().catch(console.error);
