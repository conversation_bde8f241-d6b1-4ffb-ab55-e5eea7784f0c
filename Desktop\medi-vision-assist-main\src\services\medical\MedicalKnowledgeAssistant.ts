/**
 * MediVision Medical Knowledge Assistant
 * Comprehensive medical information service that transforms OCR-detected product names
 * into structured medical profiles with complete therapeutic information.
 */

import { supabase } from '@/integrations/supabase/client';

export interface MedicalProfile {
  name: string;
  type: 'Medicine' | 'Supplement';
  description: string;
  medicalUses: string[];
  dosageGuidelines: string;
  administrationMethod: string;
  relatedProducts: string[];
  disclaimer: string;
}

export interface RxNormConcept {
  name: string;
  rxcui: string;
  tty: string;
  synonym?: string;
}

export interface RxNormDetailedInfo {
  properties?: {
    name?: string;
    strength?: string;
    dose_form?: string;
    route?: string;
  };
  relatedConcepts?: {
    conceptGroup?: Array<{
      tty: string;
      conceptProperties?: Array<{
        name: string;
        rxcui: string;
      }>;
    }>;
  };
}

export interface EnhancedRxNormResponse {
  drugGroup?: {
    conceptGroup?: Array<{
      tty?: string;
      conceptProperties?: RxNormConcept[];
    }>;
  };
}

/**
 * Medical Knowledge Assistant Service
 * Provides comprehensive medical information for OCR-detected products
 */
export class MedicalKnowledgeAssistant {
  private static readonly RXNORM_BASE_URL = 'https://rxnav.nlm.nih.gov/REST';
  private static readonly DISCLAIMER = 'Educational Purpose Only — Always consult a healthcare professional before use.';

  /**
   * Main entry point: Process OCR-detected product name and return structured medical profile
   */
  static async processOCRDetectedProduct(
    productName: string,
    brandNames?: string[],
    synonyms?: string[]
  ): Promise<MedicalProfile> {
    console.log(`🔍 Processing OCR-detected product: ${productName}`);

    try {
      // Step 1: Search RxNorm API for comprehensive data
      const rxnormData = await this.searchRxNormComprehensive(productName);
      
      if (rxnormData) {
        console.log('✅ RxNorm data found, creating comprehensive profile');
        return await this.createComprehensiveMedicalProfile(rxnormData, productName);
      }

      // Step 2: Try alternative names if provided
      if (brandNames?.length || synonyms?.length) {
        const alternativeNames = [...(brandNames || []), ...(synonyms || [])];
        
        for (const altName of alternativeNames) {
          const altData = await this.searchRxNormComprehensive(altName);
          if (altData) {
            console.log(`✅ Found data using alternative name: ${altName}`);
            return await this.createComprehensiveMedicalProfile(altData, productName);
          }
        }
      }

      // Step 3: Fallback to knowledge-based profile
      console.log('⚠️ RxNorm data not found, using knowledge-based fallback');
      return this.createKnowledgeBasedProfile(productName);

    } catch (error) {
      console.error('❌ Error processing OCR product:', error);
      return this.createErrorProfile(productName);
    }
  }

  /**
   * Enhanced RxNorm API search with multiple endpoints for comprehensive data
   */
  private static async searchRxNormComprehensive(productName: string): Promise<any> {
    try {
      const cleanName = productName.replace(/[^\w\s]/g, '').trim();
      
      // Primary search endpoint
      const searchUrl = `${this.RXNORM_BASE_URL}/drugs.json?name=${encodeURIComponent(cleanName)}`;
      console.log(`🌐 Searching RxNorm: ${searchUrl}`);

      const response = await fetch(searchUrl, {
        headers: { 'User-Agent': 'MediVision-Medical-Assistant/1.0' }
      });

      if (!response.ok) {
        throw new Error(`RxNorm API error: ${response.status}`);
      }

      const data: EnhancedRxNormResponse = await response.json();
      const concepts = data.drugGroup?.conceptGroup?.[0]?.conceptProperties;

      if (!concepts || concepts.length === 0) {
        return null;
      }

      const primaryConcept = concepts[0];
      
      // Get detailed information for the primary concept
      const detailedInfo = await this.getRxNormDetailedInfo(primaryConcept.rxcui);
      
      // Get related products
      const relatedProducts = await this.getRxNormRelatedProducts(primaryConcept.rxcui);

      return {
        primary: primaryConcept,
        details: detailedInfo,
        related: relatedProducts,
        allConcepts: concepts
      };

    } catch (error) {
      console.error('RxNorm comprehensive search failed:', error);
      return null;
    }
  }

  /**
   * Get detailed information for a specific RxCUI
   */
  private static async getRxNormDetailedInfo(rxcui: string): Promise<RxNormDetailedInfo | null> {
    try {
      // Get properties
      const propsUrl = `${this.RXNORM_BASE_URL}/rxcui/${rxcui}/properties.json`;
      const propsResponse = await fetch(propsUrl);
      
      let properties = {};
      if (propsResponse.ok) {
        const propsData = await propsResponse.json();
        properties = propsData.properties || {};
      }

      // Get related concepts (for finding similar medicines)
      const relatedUrl = `${this.RXNORM_BASE_URL}/rxcui/${rxcui}/related.json?tty=IN+PIN+MIN+BN+SBD+SCD`;
      const relatedResponse = await fetch(relatedUrl);
      
      let relatedConcepts = {};
      if (relatedResponse.ok) {
        const relatedData = await relatedResponse.json();
        relatedConcepts = relatedData.relatedGroup || {};
      }

      return {
        properties,
        relatedConcepts
      };

    } catch (error) {
      console.error('Failed to get RxNorm detailed info:', error);
      return null;
    }
  }

  /**
   * Get related products for a specific RxCUI
   */
  private static async getRxNormRelatedProducts(rxcui: string): Promise<string[]> {
    try {
      const url = `${this.RXNORM_BASE_URL}/rxcui/${rxcui}/related.json?tty=SBD+SCD+GPCK+BPCK`;
      const response = await fetch(url);

      if (!response.ok) {
        return [];
      }

      const data = await response.json();
      const relatedGroup = data.relatedGroup;
      
      if (!relatedGroup || !relatedGroup.conceptGroup) {
        return [];
      }

      const products: string[] = [];
      
      for (const group of relatedGroup.conceptGroup) {
        if (group.conceptProperties) {
          for (const concept of group.conceptProperties.slice(0, 5)) {
            if (concept.name && !products.includes(concept.name)) {
              products.push(concept.name);
            }
          }
        }
      }

      return products.slice(0, 5); // Limit to 5 related products

    } catch (error) {
      console.error('Failed to get related products:', error);
      return [];
    }
  }

  /**
   * Create comprehensive medical profile from RxNorm data
   */
  private static async createComprehensiveMedicalProfile(
    rxnormData: any,
    originalName: string
  ): Promise<MedicalProfile> {
    const { primary, details, related, allConcepts } = rxnormData;

    // Determine if it's a medicine or supplement
    const type = this.classifyProductType(primary.name, primary.tty);

    // Build comprehensive name with generic and brand names
    const name = this.buildComprehensiveName(primary, allConcepts);

    // Generate description
    const description = this.generateDescription(primary.name, type, primary.tty);

    // Get medical uses
    const medicalUses = this.getMedicalUses(primary.name, type);

    // Get dosage guidelines
    const dosageGuidelines = this.getDosageGuidelines(primary.name, details?.properties, type);

    // Get administration method
    const administrationMethod = this.getAdministrationMethod(primary.name, details?.properties, type);

    // Get related products
    const relatedProducts = related.length > 0 ? related : this.getKnowledgeBasedRelatedProducts(primary.name);

    return {
      name,
      type,
      description,
      medicalUses,
      dosageGuidelines,
      administrationMethod,
      relatedProducts,
      disclaimer: this.DISCLAIMER
    };
  }

  /**
   * Classify product as Medicine or Supplement
   */
  private static classifyProductType(productName: string, tty?: string): 'Medicine' | 'Supplement' {
    const name = productName.toLowerCase();
    
    // Supplement indicators
    const supplementKeywords = [
      'vitamin', 'mineral', 'supplement', 'calcium', 'magnesium', 'iron',
      'zinc', 'omega', 'fish oil', 'probiotics', 'multivitamin', 'b12',
      'vitamin d', 'vitamin c', 'folic acid', 'biotin', 'coenzyme'
    ];

    // Medicine indicators (TTY codes from RxNorm)
    const medicineTypes = ['SBD', 'SCD', 'GPCK', 'BPCK', 'PSN', 'PIN', 'MIN'];
    
    if (tty && medicineTypes.includes(tty)) {
      return 'Medicine';
    }

    if (supplementKeywords.some(keyword => name.includes(keyword))) {
      return 'Supplement';
    }

    // Default to Medicine for medical products
    return 'Medicine';
  }

  /**
   * Build comprehensive name with generic and brand names
   */
  private static buildComprehensiveName(primary: RxNormConcept, allConcepts: RxNormConcept[]): string {
    const names = new Set<string>();
    names.add(primary.name);

    // Add synonyms and alternative names
    if (primary.synonym) {
      names.add(primary.synonym);
    }

    // Add other concept names (brand names, generic names)
    allConcepts.slice(0, 3).forEach(concept => {
      if (concept.name !== primary.name) {
        names.add(concept.name);
      }
    });

    return Array.from(names).join(' / ');
  }

  /**
   * Generate comprehensive description
   */
  private static generateDescription(productName: string, type: 'Medicine' | 'Supplement', tty?: string): string {
    const name = productName.toLowerCase();
    
    // Medicine-specific descriptions
    const medicineDescriptions: Record<string, string> = {
      'aspirin': 'A widely used nonsteroidal anti-inflammatory drug (NSAID) developed for pain relief, fever reduction, and cardiovascular protection. It works by inhibiting cyclooxygenase enzymes.',
      'ibuprofen': 'A nonsteroidal anti-inflammatory drug (NSAID) that reduces inflammation, pain, and fever. It works by blocking the production of prostaglandins.',
      'acetaminophen': 'A pain reliever and fever reducer that works by affecting the heat-regulating center of the brain. It is gentler on the stomach than NSAIDs.',
      'paracetamol': 'A pain reliever and fever reducer that works by affecting the heat-regulating center of the brain. It is gentler on the stomach than NSAIDs.',
      'metformin': 'An oral diabetes medication that helps control blood sugar levels by decreasing glucose production in the liver and improving insulin sensitivity.',
      'lisinopril': 'An ACE inhibitor used to treat high blood pressure and heart failure by relaxing blood vessels and reducing the workload on the heart.'
    };

    // Supplement-specific descriptions
    const supplementDescriptions: Record<string, string> = {
      'vitamin d': 'An essential nutrient that helps the body absorb calcium and maintain bone health. Also supports immune function and muscle strength.',
      'vitamin c': 'A water-soluble vitamin essential for immune function, collagen synthesis, and antioxidant protection against cellular damage.',
      'calcium': 'An essential mineral crucial for bone and teeth health, muscle function, and nerve transmission. Often supplemented to prevent osteoporosis.',
      'magnesium': 'An essential mineral involved in over 300 enzymatic reactions, supporting muscle and nerve function, blood glucose control, and bone health.',
      'omega 3': 'Essential fatty acids that support heart health, brain function, and reduce inflammation throughout the body.'
    };

    // Check for specific matches
    for (const [key, desc] of Object.entries(type === 'Medicine' ? medicineDescriptions : supplementDescriptions)) {
      if (name.includes(key)) {
        return desc;
      }
    }

    // Generic descriptions based on type
    if (type === 'Supplement') {
      return `A nutritional supplement designed to provide essential nutrients that may be missing from your regular diet. Supports overall health and wellness when used as part of a balanced lifestyle.`;
    } else {
      return `A pharmaceutical medication developed to treat specific medical conditions. Works through targeted therapeutic mechanisms to provide relief and improve health outcomes.`;
    }
  }
