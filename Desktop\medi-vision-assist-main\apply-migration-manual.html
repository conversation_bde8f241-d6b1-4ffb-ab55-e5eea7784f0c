<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply Supabase Migration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        .copy-btn:hover {
            background: #218838;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Apply MediVision Database Migration</h1>
            <p>This will create the medicines table and search functions in your Supabase database</p>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> This migration will create new tables and functions in your Supabase database. Make sure you're applying this to the correct project.
        </div>

        <div class="step">
            <h3>Step 1: Open Supabase Dashboard</h3>
            <p>1. Go to <a href="https://supabase.com/dashboard" target="_blank">https://supabase.com/dashboard</a></p>
            <p>2. Select your project: <strong>ygkxdctaraeragizxfbt</strong></p>
            <p>3. Click on <strong>"SQL Editor"</strong> in the left sidebar</p>
        </div>

        <div class="step">
            <h3>Step 2: Copy the Migration SQL</h3>
            <p>Click the button below to copy the migration SQL to your clipboard:</p>
            <button class="copy-btn" onclick="copyMigrationSQL()">📋 Copy Migration SQL</button>
            
            <div class="sql-code" id="migrationSQL">-- Loading migration SQL...</div>
        </div>

        <div class="step">
            <h3>Step 3: Execute in Supabase</h3>
            <p>1. Paste the copied SQL into the SQL Editor</p>
            <p>2. Click <strong>"Run"</strong> to execute the migration</p>
            <p>3. Wait for the success message</p>
        </div>

        <div class="success">
            <strong>✅ After Success:</strong> Your database will have the medicines table and search functions. The OCR errors should be resolved!
        </div>
    </div>

    <script>
        // Load the migration SQL
        async function loadMigrationSQL() {
            try {
                const response = await fetch('./supabase/migrations/20250806000000_create_medicines_database.sql');
                const sql = await response.text();
                document.getElementById('migrationSQL').textContent = sql;
            } catch (error) {
                document.getElementById('migrationSQL').textContent = 'Error loading migration file. Please copy manually from: supabase/migrations/20250806000000_create_medicines_database.sql';
            }
        }

        function copyMigrationSQL() {
            const sqlElement = document.getElementById('migrationSQL');
            const sql = sqlElement.textContent;
            
            navigator.clipboard.writeText(sql).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                btn.style.background = '#28a745';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#28a745';
                }, 2000);
            }).catch(() => {
                alert('Failed to copy. Please select and copy the SQL manually.');
            });
        }

        // Load the SQL when page loads
        loadMigrationSQL();
    </script>
</body>
</html>
