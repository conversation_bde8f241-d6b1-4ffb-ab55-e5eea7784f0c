import React, { useState, useRef, useEffect } from 'react';
import { Upload, Camera, X, Loader2, Lightbulb, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/integrations/supabase/client';
import { MedicalOCR } from '@/services/ocr/MedicalOCR';
import { ServerOCRService } from '@/services/ocr/ServerOCRService';
import { MedicineDatabaseService } from '@/services/database/MedicineDatabase';
import { OCRResult, MedicineIdentificationResult } from '@/types/ocr';
import { OCRScanService } from '@/services/supabase/ocrScanService';
import { useAuth } from '@/contexts/AuthContext';
import { filterOCRKeywords, scoreKeywords } from '@/utils/keywordFilter';

interface ImageUploadProps {
  onResult: (result: any) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onResult }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedText, setExtractedText] = useState<string>('');
  const [ocrProgress, setOcrProgress] = useState<string>('');
  const [showOCRTips, setShowOCRTips] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('auto');
  const [useAutoDetection, setUseAutoDetection] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const ocrEngineRef = useRef<MedicalOCR | null>(null);
  const serverOCRRef = useRef<ServerOCRService | null>(null);

  // Check if OCR tips should be shown (once per day per user)
  const shouldShowOCRTips = () => {
    if (!user) return false;

    const today = new Date().toDateString();
    const storageKey = `ocr-tips-shown-${user.id}`;
    const lastShown = localStorage.getItem(storageKey);

    return lastShown !== today;
  };

  // Mark OCR tips as shown for today
  const markOCRTipsShown = () => {
    if (!user) return;

    const today = new Date().toDateString();
    const storageKey = `ocr-tips-shown-${user.id}`;
    localStorage.setItem(storageKey, today);
  };

  // Initialize OCR engines
  useEffect(() => {
    // Determine language configuration based on user selection
    const getLanguageConfig = () => {
      if (useAutoDetection || selectedLanguage === 'auto') {
        return {
          languages: ['eng', 'fra', 'deu'], // Support English, French, German
          autoDetectLanguage: true,
          language: 'eng' // Default fallback
        };
      } else {
        return {
          languages: [selectedLanguage],
          autoDetectLanguage: false,
          language: selectedLanguage
        };
      }
    };

    // Initialize client-side OCR with language configuration
    const langConfig = getLanguageConfig();
    ocrEngineRef.current = new MedicalOCR({
      ...langConfig,
      minConfidence: 50,
      debug: true,
    });

    // Initialize server-side OCR service
    serverOCRRef.current = new ServerOCRService({
      serverUrl: 'http://localhost:3001',
      timeout: 30000,
      fallbackToClient: true
    });

    return () => {
      // Cleanup OCR engines on unmount
      if (ocrEngineRef.current) {
        ocrEngineRef.current.terminate();
      }
    };
  }, [selectedLanguage, useAutoDetection]);

  const handleImageSelect = (file: File) => {
    setSelectedImage(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setExtractedText('');

    // Show OCR tips popup if it should be shown today
    if (shouldShowOCRTips()) {
      setShowOCRTips(true);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageSelect(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setExtractedText('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Known brand medicine dictionary - CRITICAL for accurate identification
  const knownBrandMedicines = [
    // Aspirin family
    { patterns: ['aspirin'], name: 'Aspirin', confidence: 98, category: 'pain_relief' },
    { patterns: ['aspirin extra', 'aspirin extra strength'], name: 'Aspirin Extra Strength', confidence: 98, category: 'pain_relief' },
    
    // Antibiotics
    { patterns: ['amoxicillin', 'amoxicillin sandoz'], name: 'Amoxicillin', confidence: 98, category: 'antibiotic' },
    { patterns: ['amoxicillin sandoz'], name: 'Amoxicillin Sandoz', confidence: 99, category: 'antibiotic' },
    { patterns: ['augmentin'], name: 'Augmentin', confidence: 98, category: 'antibiotic' },
    { patterns: ['zithromax'], name: 'Zithromax', confidence: 98, category: 'antibiotic' },
    
    // Paracetamol/Acetaminophen family
    { patterns: ['panadol'], name: 'Panadol', confidence: 98, category: 'pain_relief' },
    { patterns: ['panadol extra'], name: 'Panadol Extra', confidence: 99, category: 'pain_relief' },
    { patterns: ['tylenol'], name: 'Tylenol', confidence: 98, category: 'pain_relief' },
    { patterns: ['paracetamol'], name: 'Paracetamol', confidence: 95, category: 'pain_relief' },
    { patterns: ['acetaminophen'], name: 'Acetaminophen', confidence: 95, category: 'pain_relief' },
    
    // NSAIDs
    { patterns: ['advil'], name: 'Advil', confidence: 98, category: 'pain_relief' },
    { patterns: ['motrin'], name: 'Motrin', confidence: 98, category: 'pain_relief' },
    { patterns: ['ibuprofen'], name: 'Ibuprofen', confidence: 95, category: 'pain_relief' },
    { patterns: ['voltaren'], name: 'Voltaren', confidence: 98, category: 'pain_relief' },
    { patterns: ['diclofenac'], name: 'Diclofenac', confidence: 95, category: 'pain_relief' },
    { patterns: ['naproxen'], name: 'Naproxen', confidence: 95, category: 'pain_relief' },
    { patterns: ['aleve'], name: 'Aleve', confidence: 98, category: 'pain_relief' },
    
    // Other categories
    { patterns: ['metformin'], name: 'Metformin', confidence: 98, category: 'diabetes' },
    { patterns: ['lisinopril'], name: 'Lisinopril', confidence: 98, category: 'blood_pressure' },
    { patterns: ['amlodipine'], name: 'Amlodipine', confidence: 98, category: 'blood_pressure' },
    { patterns: ['omeprazole'], name: 'Omeprazole', confidence: 98, category: 'stomach_acid' },
    { patterns: ['benadryl'], name: 'Benadryl', confidence: 98, category: 'allergy' },
    { patterns: ['claritin'], name: 'Claritin', confidence: 98, category: 'allergy' },
  ];

  // Strict brand-first identification with category validation
  const identifyMedicineFromText = (text: string): { name: string; confidence: number } | null => {
    const cleanText = text.toLowerCase().replace(/[_-]/g, ' ').trim();
    console.log(`🔍 Analyzing text: "${cleanText}"`);
    
    let bestMatch = { name: '', confidence: 0, category: '' };
    
    // STEP 1: Look for exact brand matches (highest priority)
    for (const medicine of knownBrandMedicines) {
      for (const pattern of medicine.patterns) {
        if (cleanText.includes(pattern.toLowerCase())) {
          const matchConfidence = medicine.confidence;
          console.log(`✅ Brand match found: "${pattern}" -> ${medicine.name} (${matchConfidence}%)`);
          
          if (matchConfidence > bestMatch.confidence) {
            bestMatch = {
              name: medicine.name,
              confidence: matchConfidence,
              category: medicine.category
            };
          }
        }
      }
    }
    
    // STEP 2: Validate the match makes sense
    if (bestMatch.confidence > 0) {
      console.log(`🎯 Final identification: ${bestMatch.name} (${bestMatch.confidence}% confidence)`);
      return { name: bestMatch.name, confidence: bestMatch.confidence };
    }
    
    console.log(`❌ No confident match found for: "${cleanText}"`);
    return null;
  };

  // Real OCR implementation using Tesseract.js with comprehensive error handling
  const performRealOCR = async (file: File): Promise<MedicineIdentificationResult | null> => {
    if (!ocrEngineRef.current) {
      console.error('❌ OCR engine not initialized');
      setOcrProgress('OCR engine initialization failed');
      return null;
    }

    // Validate file before processing
    if (!file || file.size === 0) {
      console.error('❌ Invalid file provided to OCR');
      setOcrProgress('Invalid file provided');
      return null;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      console.error('❌ File too large for OCR processing');
      setOcrProgress('File too large (max 10MB)');
      return null;
    }

    const startTime = Date.now();

    try {
      setOcrProgress('Initializing OCR engine...');
      console.log(`🔄 Starting real OCR processing for: ${file.name} (${(file.size / 1024).toFixed(1)}KB)`);

      // Initialize OCR engine with timeout
      const initPromise = ocrEngineRef.current.initialize();
      const initTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('OCR initialization timeout (30s)')), 30000)
      );

      await Promise.race([initPromise, initTimeout]);

      setOcrProgress('Processing image (this may take 30-60 seconds)...');

      // Perform complete medicine identification with timeout
      const ocrPromise = ocrEngineRef.current.identifyMedicine(file);
      const ocrTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('OCR processing timeout (120s)')), 120000)
      );

      const result = await Promise.race([ocrPromise, ocrTimeout]) as MedicineIdentificationResult;
      const processingTime = Date.now() - startTime;

      setOcrProgress('Analyzing results...');

      if (result.success && result.identifiedMedicine) {
        console.log(`✅ OCR successfully identified: ${result.identifiedMedicine}`);
        console.log(`📊 OCR confidence: ${result.ocrResult.confidence.toFixed(1)}%`);
        console.log(`🎯 Medicine confidence: ${result.medicineInfo.confidence}%`);
        console.log(`⏱️ Processing time: ${processingTime}ms`);

        return result;
      } else {
        console.log('⚠️ OCR failed to identify medicine with sufficient confidence');
        console.log(`📊 OCR confidence: ${result.ocrResult.confidence.toFixed(1)}%`);
        console.log(`🎯 Medicine confidence: ${result.medicineInfo.confidence}%`);
        console.log(`⏱️ Processing time: ${processingTime}ms`);

        // Try fallback to filename analysis if OCR confidence is low
        const filenameResult = identifyMedicineFromText(file.name);
        if (filenameResult && filenameResult.confidence >= 90) {
          console.log(`🔄 Falling back to filename analysis: ${filenameResult.name}`);
          return {
            ...result,
            identifiedMedicine: filenameResult.name,
            success: true,
          };
        }

        return result;
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('❌ OCR processing failed:', error);
      console.log(`⏱️ Failed after: ${processingTime}ms`);

      // Provide specific error messages based on error type
      let errorMessage = 'OCR processing failed';
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'OCR processing timed out - try a smaller or clearer image';
        } else if (error.message.includes('memory')) {
          errorMessage = 'Insufficient memory for OCR - try a smaller image';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error during OCR processing';
        } else {
          errorMessage = `OCR error: ${error.message}`;
        }
      }

      setOcrProgress(errorMessage);

      // Try filename analysis as last resort even if OCR crashed
      try {
        const filenameResult = identifyMedicineFromText(file.name);
        if (filenameResult && filenameResult.confidence >= 90) {
          console.log(`🔄 Using filename analysis after OCR failure: ${filenameResult.name}`);
          return {
            ocrResult: {
              text: file.name,
              confidence: 0,
              processingTime,
              words: [],
              success: false,
              error: errorMessage
            },
            medicineInfo: {
              potentialNames: [filenameResult.name],
              confidence: filenameResult.confidence,
              rawText: file.name,
              cleanedText: file.name.toLowerCase()
            },
            identifiedMedicine: filenameResult.name,
            success: true
          };
        }
      } catch (filenameError) {
        console.error('❌ Filename analysis also failed:', filenameError);
      }

      return null;
    } finally {
      setOcrProgress('');
    }
  };

  // Store OCR results in Supabase using the OCRScanService
  const storeOCRResult = async (
    ocrResult: MedicineIdentificationResult,
    status: 'completed' | 'failed',
    medicineResult?: any
  ) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.log('⚠️ No authenticated user, skipping OCR result storage');
        return null;
      }

      const ocrRecord = {
        user_id: user.id,
        image_url: selectedImage?.name || 'unknown', // Store filename in image_url for now
        extracted_text: ocrResult.ocrResult.text,
        confidence_score: Math.round(ocrResult.ocrResult.confidence),
        medicine_name: ocrResult.identifiedMedicine || null,
        medicine_type: ocrResult.medicineInfo.medicineType || null,
        usage_info: medicineResult?.medicine?.usage || null,
        warnings: medicineResult?.medicine?.warnings || null,
        side_effects: medicineResult?.medicine?.side_effects || null,
        scan_status: status,
      };

      const result = await OCRScanService.createScanRecord(ocrRecord);

      if (result) {
        console.log('✅ OCR result stored successfully with ID:', result.id);
        return result;
      } else {
        console.error('❌ Failed to store OCR result');
        return null;
      }
    } catch (error) {
      console.error('❌ Error storing OCR result:', error);
      return null;
    }
  };

  const lookupMedicine = async (medicineName: string) => {
    try {
      console.log(`🔍 Looking up medicine in database: ${medicineName}`);

      // Validate input
      if (!medicineName || typeof medicineName !== 'string' || medicineName.trim().length === 0) {
        console.error('❌ Invalid medicine name provided for lookup');
        return {
          confidence: 0,
          medicine: {
            product: 'Invalid Input',
            type: 'Error',
            usage: 'No medicine name was provided for lookup. Please try again.',
            dosage: 'N/A',
            similar: 'Please provide a valid medicine name'
          },
          error: true
        };
      }

      // Use the new comprehensive medicine database with timeout
      const lookupPromise = MedicineDatabaseService.lookupMedicine(medicineName);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database lookup timeout (10s)')), 10000)
      );

      const result = await Promise.race([lookupPromise, timeoutPromise]) as any;

      if (result.success && result.medicine) {
        console.log('✅ Medicine found in database:', result.medicine.generic_name);

        return {
          confidence: result.confidence,
          medicine: {
            product: result.medicine.generic_name,
            type: result.medicine.medicine_type,
            usage: (result.medicine.indications && Array.isArray(result.medicine.indications))
              ? result.medicine.indications.join(', ')
              : 'Consult healthcare provider',
            dosage: result.medicine.typical_dosage || 'As prescribed',
            similar: (result.medicine.brand_names && Array.isArray(result.medicine.brand_names))
              ? result.medicine.brand_names.join(', ')
              : 'None listed',
            strength: result.medicine.strength || 'Not specified',
            warnings: (result.medicine.warnings && Array.isArray(result.medicine.warnings))
              ? result.medicine.warnings.join('; ')
              : (result.medicine.contraindications && Array.isArray(result.medicine.contraindications))
                ? result.medicine.contraindications.join('; ')
                : 'None listed',
            sideEffects: (result.medicine.side_effects && Array.isArray(result.medicine.side_effects))
              ? result.medicine.side_effects.join(', ')
              : 'None listed',
            prescriptionRequired: result.medicine.prescription_required || false
          },
          databaseId: result.medicine.id
        };
      } else {
        console.log('⚠️ Medicine not found in database, showing suggestions');

        const suggestions = result.suggestions?.slice(0, 3).map(s => s.generic_name).join(', ') || '';

        return {
          confidence: result.confidence,
          medicine: {
            product: medicineName,
            type: 'Unknown',
            usage: result.message || 'This medicine was not found in our database. Please verify the name or consult a healthcare provider.',
            dosage: 'Consult healthcare provider',
            similar: suggestions || 'Try a more specific name'
          },
          suggestions: result.suggestions || [],
          error: result.confidence < 50
        };
      }
    } catch (error) {
      console.error('❌ Error looking up medicine:', error);

      let errorMessage = 'Unable to access medicine database. Please try again or consult a healthcare provider.';
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'Database lookup timed out. Please check your connection and try again.';
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error while accessing database. Please check your connection.';
        }
      }

      return {
        confidence: 0,
        medicine: {
          product: medicineName || 'Database Error',
          type: 'Error',
          usage: errorMessage,
          dosage: 'Consult healthcare provider',
          similar: 'Try again or use manual search'
        },
        error: true
      };
    }
  };

  // Advanced OCR processing (slow but thorough client-side OCR)
  const processAdvancedOCR = async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setExtractedText('');
    setOcrProgress('Starting advanced OCR processing...');

    try {
      console.log('🔄 Starting advanced client-side OCR processing...');
      console.log('⚠️ This may take 30-60 seconds for thorough analysis...');

      // Use the existing performRealOCR function but with user consent
      const ocrResult = await performRealOCR(selectedImage);

      if (ocrResult && ocrResult.success && ocrResult.identifiedMedicine) {
        const identifiedMedicine = ocrResult.identifiedMedicine;
        setExtractedText(identifiedMedicine);
        console.log(`✅ Advanced OCR identified: ${identifiedMedicine}`);

        const medicineResult = await lookupMedicine(identifiedMedicine);

        const finalResult = {
          ...medicineResult,
          ocrData: {
            extractedText: ocrResult.ocrResult.text,
            confidence: ocrResult.ocrResult.confidence,
            processingTime: ocrResult.ocrResult.processingTime,
            medicineConfidence: ocrResult.medicineInfo.confidence,
            source: 'advanced_client'
          }
        };

        onResult(finalResult);
      } else {
        console.log('❌ Advanced OCR also failed to identify medicine');
        setExtractedText('Advanced OCR failed');

        onResult({
          confidence: 0,
          medicine: {
            product: 'Advanced OCR Failed',
            type: 'Processing Failed',
            usage: 'Even the advanced OCR processing could not identify the medicine. Please try manual search or take a clearer photo.',
            dosage: 'Use manual search instead',
            similar: 'Try alternative input methods'
          },
          error: true,
          ocrData: ocrResult ? {
            extractedText: ocrResult.ocrResult.text,
            confidence: ocrResult.ocrResult.confidence,
            processingTime: ocrResult.ocrResult.processingTime,
            source: 'advanced_client_failed'
          } : null
        });
      }
    } catch (error) {
      console.error('❌ Advanced OCR processing failed:', error);

      onResult({
        confidence: 0,
        medicine: {
          product: 'Advanced OCR Error',
          type: 'Processing Error',
          usage: 'An error occurred during advanced OCR processing. Please try manual search.',
          dosage: 'Use manual search instead',
          similar: 'Try alternative input methods'
        },
        error: true,
        ocrData: {
          extractedText: 'Error occurred',
          confidence: 0,
          processingTime: 0,
          source: 'advanced_error'
        }
      });
    } finally {
      setIsProcessing(false);
      setOcrProgress('');
    }
  };

  const processImage = async () => {
    if (!selectedImage) return;

    setIsProcessing(true);
    setExtractedText('');

    try {
      console.log('🚀 Starting intelligent OCR image processing...');

      // Step 1: Try server-side OCR first (ALWAYS prioritize server results)
      let serverResult: any = null;
      let serverKeywords: string[] = [];

      if (serverOCRRef.current?.isAvailable()) {
        setOcrProgress('Processing on server...');

        try {
          serverResult = await serverOCRRef.current.processImage(selectedImage);

          if (serverResult.success && serverResult.medicineNames.length > 0) {
            serverKeywords = serverResult.medicineNames;
            console.log(`🎯 Server OCR found keywords: ${serverKeywords.join(', ')} (${serverResult.confidence}% confidence)`);

            // If server confidence is high enough, use it directly
            if (serverResult.confidence >= 60) {
              console.log('✅ Server OCR confidence acceptable, using result directly');
              const identifiedMedicine = serverKeywords[0];
              setExtractedText(identifiedMedicine);

              const medicineResult = await lookupMedicine(identifiedMedicine);

              const finalResult = {
                ...medicineResult,
                ocrData: {
                  extractedText: serverResult.text,
                  confidence: serverResult.confidence,
                  processingTime: serverResult.processingTime,
                  medicineConfidence: serverResult.confidence,
                  source: 'server'
                }
              };

              onResult(finalResult);
              return;
            }
          }
        } catch (error) {
          console.warn('⚠️ Server OCR failed, will try alternatives:', error);
        }
      }

      // Step 2: If server OCR found keywords but low confidence, present suggestions
      if (serverKeywords.length > 0) {
        console.log('🔄 Server found keywords but low confidence, presenting suggestions...');
        setOcrProgress('Analyzing server results...');

        // Filter and clean the keywords before database lookup
        const filteredKeywords = filterOCRKeywords(serverKeywords);
        console.log(`🧹 Filtered keywords: ${serverKeywords.join(', ')} → ${filteredKeywords.join(', ')}`);

        if (filteredKeywords.length === 0) {
          console.log('⚠️ No valid keywords after filtering');
          // Continue to next step (filename analysis)
        } else {
          // Try to lookup each filtered keyword and find the best match
          let bestMatch: any = null;
          let bestConfidence = 0;

          for (const keyword of filteredKeywords.slice(0, 3)) { // Try top 3 filtered keywords
            try {
              const lookupResult = await lookupMedicine(keyword);
              if (lookupResult.confidence > bestConfidence) {
                bestMatch = lookupResult;
                bestConfidence = lookupResult.confidence;
              }
            } catch (error) {
              console.warn(`⚠️ Failed to lookup keyword: ${keyword}`, error);
            }
          }

          if (bestMatch && bestConfidence >= 50) {
            console.log(`✅ Found good match from server keywords: ${bestConfidence}% confidence`);
            setExtractedText(filteredKeywords[0]);

            const finalResult = {
              ...bestMatch,
              ocrData: {
                extractedText: serverResult?.text || '',
                confidence: serverResult?.confidence || 0,
                processingTime: serverResult?.processingTime || 0,
                medicineConfidence: bestConfidence,
                source: 'server_keywords',
                suggestions: filteredKeywords
              }
            };

            onResult(finalResult);
            return;
          }

          // If no good database matches, present filtered suggestions to user
          console.log('🤔 Presenting filtered keyword suggestions to user...');
          setExtractedText(`Suggestions: ${filteredKeywords.join(', ')}`);

          onResult({
            confidence: serverResult?.confidence || 0,
            medicine: {
              product: 'Multiple Options Found',
              type: 'Suggestions',
              usage: `We found these potential medicines: ${filteredKeywords.join(', ')}. Please verify which one matches your medicine.`,
              dosage: 'Please verify the correct medicine',
              similar: filteredKeywords.join(', ')
            },
            suggestions: filteredKeywords,
            ocrData: {
              extractedText: serverResult?.text || '',
              confidence: serverResult?.confidence || 0,
              processingTime: serverResult?.processingTime || 0,
              source: 'server_suggestions'
            }
          });
          return;
        }
      }

      // Step 3: Try filename analysis as a lightweight fallback
      console.log('🔄 Trying filename analysis...');
      setOcrProgress('Analyzing filename...');

      const filenameResult = identifyMedicineFromText(selectedImage.name);
      if (filenameResult && filenameResult.confidence >= 90) {
        console.log(`✅ Filename analysis successful: ${filenameResult.name}`);
        setExtractedText(filenameResult.name);

        const medicineResult = await lookupMedicine(filenameResult.name);

        const finalResult = {
          ...medicineResult,
          ocrData: {
            extractedText: selectedImage.name,
            confidence: filenameResult.confidence,
            processingTime: 0,
            medicineConfidence: filenameResult.confidence,
            source: 'filename'
          }
        };

        onResult(finalResult);
        return;
      }

      // Step 4: Offer client-side OCR as an optional last resort
      console.log('⚠️ All lightweight methods failed, offering client-side OCR option...');
      setExtractedText('Unable to identify automatically');

      onResult({
        confidence: 0,
        medicine: {
          product: 'Unable to Identify Medicine Automatically',
          type: 'Manual Input Required',
          usage: 'The image could not be processed automatically. This could be due to:\n• Image quality (try better lighting or focus)\n• Unusual medicine packaging\n• Text in unsupported language\n\nOptions:\n1. Take a clearer photo and try again\n2. Use the manual search feature below\n3. Describe the medicine instead\n4. Try advanced OCR processing (slower but more thorough)',
          dosage: 'Use manual search or description features',
          similar: 'Try manual input methods below'
        },
        error: true,
        showAdvancedOCR: true, // Flag to show advanced OCR option
        ocrData: {
          extractedText: serverResult?.text || 'No text extracted',
          confidence: serverResult?.confidence || 0,
          processingTime: serverResult?.processingTime || 0,
          source: 'failed_processing'
        }
      });

    } catch (error) {
      console.error('❌ Error processing image:', error);

      onResult({
        confidence: 0,
        medicine: {
          product: 'Processing Error',
          type: 'System Error',
          usage: 'An unexpected error occurred while processing the image. Please try again or use manual search.',
          dosage: 'Try manual search instead',
          similar: 'Use alternative input methods'
        },
        error: true,
        ocrData: {
          extractedText: 'Error occurred',
          confidence: 0,
          processingTime: 0,
          source: 'error'
        }
      });
    } finally {
      setIsProcessing(false);
      setOcrProgress('');
    }
  };

  // OCR Tips Dialog Component
  const OCRTipsDialog = () => (
    <Dialog open={showOCRTips} onOpenChange={setShowOCRTips}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5 text-yellow-500" />
            <span>OCR Photography Tips</span>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground mb-4">
            Follow these tips for better medicine identification results:
          </div>

          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Camera className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-sm">Take a clear photo</div>
                <div className="text-xs text-muted-foreground">Make sure the image is not blurry or shaky.</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-sm">Good lighting is key</div>
                <div className="text-xs text-muted-foreground">Avoid shadows or dim environments. Natural light works best.</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0 text-xs font-bold">🏷️</div>
              <div>
                <div className="font-medium text-sm">Center the medicine name</div>
                <div className="text-xs text-muted-foreground">Place the main label or text in the center of the image.</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0 text-xs font-bold">🧱</div>
              <div>
                <div className="font-medium text-sm">Use a plain background</div>
                <div className="text-xs text-muted-foreground">Put the medicine on a flat, solid-colored surface.</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0 text-xs font-bold">↔️</div>
              <div>
                <div className="font-medium text-sm">Avoid tilted angles</div>
                <div className="text-xs text-muted-foreground">Take the photo directly from above or in front (not diagonally).</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0 text-xs font-bold">🔍</div>
              <div>
                <div className="font-medium text-sm">Avoid reflections or glare</div>
                <div className="text-xs text-muted-foreground">Remove plastic wrapping if it reflects light.</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0 text-xs font-bold">🚫</div>
              <div>
                <div className="font-medium text-sm">Don't cover the text</div>
                <div className="text-xs text-muted-foreground">Ensure your fingers or other objects don't block the label.</div>
              </div>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <Button
              onClick={() => {
                setShowOCRTips(false);
                markOCRTipsShown();
              }}
              className="flex items-center space-x-2"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Got it!</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      <OCRTipsDialog />

      {/* Language Selection Card */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Label className="text-sm font-medium">OCR Language Settings</Label>
              <p className="text-xs text-gray-500">
                Choose the language for better text recognition accuracy
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="auto-detect"
                  checked={useAutoDetection}
                  onCheckedChange={(checked) => {
                    setUseAutoDetection(checked as boolean);
                    if (checked) setSelectedLanguage('auto');
                  }}
                />
                <Label htmlFor="auto-detect" className="text-sm">Auto-detect</Label>
              </div>
              <Select
                value={selectedLanguage}
                onValueChange={setSelectedLanguage}
                disabled={useAutoDetection}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto-detect</SelectItem>
                  <SelectItem value="eng">English</SelectItem>
                  <SelectItem value="fra">French</SelectItem>
                  <SelectItem value="deu">German</SelectItem>
                  <SelectItem value="ara">Arabic</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          {!previewUrl ? (
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">
                {t('identify.uploadInstruction')}
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Take a clear photo of the medicine label or packaging
              </p>
              <Button variant="outline">
                <Camera className="h-4 w-4 mr-2" />
                Choose Image
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Selected medicine"
                  className="w-full max-h-64 object-contain rounded-lg border"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={removeImage}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              {extractedText && (
                <Alert>
                  <AlertDescription>
                    <strong>Detected medicine:</strong> {extractedText}
                  </AlertDescription>
                </Alert>
              )}

              {ocrProgress && (
                <Alert>
                  <AlertDescription>
                    <strong>OCR Status:</strong> {ocrProgress}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Button
                    onClick={processImage}
                    disabled={isProcessing}
                    className="flex-1"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {ocrProgress || 'Processing...'}
                      </>
                    ) : (
                      'Identify Medicine'
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Change Image
                  </Button>
                </div>

                {/* Advanced OCR Option - shown when standard processing fails */}
                {extractedText === 'Unable to identify automatically' && !isProcessing && (
                  <div className="border rounded-lg p-3 bg-amber-50 border-amber-200">
                    <div className="flex items-start space-x-2 mb-2">
                      <div className="text-amber-600 text-sm">⚠️</div>
                      <div className="text-sm text-amber-800">
                        <strong>Standard processing failed.</strong> Try advanced OCR for difficult images.
                      </div>
                    </div>
                    <Button
                      onClick={processAdvancedOCR}
                      disabled={isProcessing}
                      variant="outline"
                      size="sm"
                      className="w-full border-amber-300 text-amber-700 hover:bg-amber-100"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Advanced Processing...
                        </>
                      ) : (
                        <>
                          <div className="h-4 w-4 mr-2">🔍</div>
                          Try Advanced OCR (slower, more thorough)
                        </>
                      )}
                    </Button>
                    <div className="text-xs text-amber-600 mt-1">
                      This may take 30-60 seconds but can handle difficult images better.
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </CardContent>
      </Card>

      <Alert>
        <AlertDescription>
          <strong>Real OCR Enabled:</strong> This system now uses Tesseract.js for real optical character recognition.
          Upload clear images of medicine labels or packaging for best results. The system will extract text from the image
          and identify medicines automatically. Processing may take 10-30 seconds depending on image complexity.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default ImageUpload;
