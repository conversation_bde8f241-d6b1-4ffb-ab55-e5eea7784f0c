-- Simple fallback search function without similarity extensions
-- Use this if the main function still fails

-- Drop the function if it exists
DROP FUNCTION IF EXISTS public.search_medicines(TEXT, INTEGER, REAL);

-- Create a simpler version using basic text search
CREATE OR REPLACE FUNCTION public.search_medicines(
    search_term TEXT,
    limit_count INTEGER DEFAULT 10,
    similarity_threshold REAL DEFAULT 0.3
)
RETURNS TABLE (
    id UUID,
    generic_name TEXT,
    brand_names TEXT[],
    medicine_type TEXT,
    strength TEXT,
    indications TEXT[],
    similarity_score REAL,
    search_rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.generic_name,
        m.brand_names,
        m.medicine_type,
        m.strength,
        m.indications,
        -- Simple scoring based on exact matches and contains
        CASE 
            WHEN LOWER(m.generic_name) = LOWER(search_term) THEN 1.0
            WHEN LOWER(m.generic_name) LIKE '%' || LOWER(search_term) || '%' THEN 0.8
            WHEN EXISTS (
                SELECT 1 FROM unnest(m.brand_names) AS brand_name
                WHERE LOWER(brand_name) = LOWER(search_term)
            ) THEN 0.9
            WHEN EXISTS (
                SELECT 1 FROM unnest(m.brand_names) AS brand_name
                WHERE LOWER(brand_name) LIKE '%' || LOWER(search_term) || '%'
            ) THEN 0.7
            WHEN EXISTS (
                SELECT 1 FROM unnest(m.alternative_names) AS alt_name
                WHERE LOWER(alt_name) = LOWER(search_term)
            ) THEN 0.85
            WHEN EXISTS (
                SELECT 1 FROM unnest(m.alternative_names) AS alt_name
                WHERE LOWER(alt_name) LIKE '%' || LOWER(search_term) || '%'
            ) THEN 0.6
            ELSE 0.5
        END as similarity_score,
        -- Simple text search ranking
        CASE 
            WHEN m.search_vector @@ plainto_tsquery('english', search_term) THEN 1.0
            ELSE 0.0
        END as search_rank
    FROM public.medicines m
    WHERE 
        -- Basic text matching
        LOWER(m.generic_name) LIKE '%' || LOWER(search_term) || '%'
        OR EXISTS (
            SELECT 1 FROM unnest(m.brand_names) AS brand_name
            WHERE LOWER(brand_name) LIKE '%' || LOWER(search_term) || '%'
        )
        OR EXISTS (
            SELECT 1 FROM unnest(m.alternative_names) AS alt_name
            WHERE LOWER(alt_name) LIKE '%' || LOWER(search_term) || '%'
        )
        OR m.search_vector @@ plainto_tsquery('english', search_term)
    ORDER BY 
        similarity_score DESC,
        search_rank DESC,
        m.popularity_score DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.search_medicines TO anon, authenticated;

-- Test the function
SELECT * FROM public.search_medicines('acetaminophen', 5, 0.3);
