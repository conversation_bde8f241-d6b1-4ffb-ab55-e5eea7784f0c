/**
 * Utility functions for filtering and cleaning OCR keywords
 * before sending them to the medicine database
 */

// Common non-medicinal words that should be filtered out
const STOP_WORDS = new Set([
  // Common words
  'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
  'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above',
  'below', 'between', 'among', 'under', 'over', 'within', 'without', 'against',
  
  // Medicine packaging words
  'tablet', 'tablets', 'capsule', 'capsules', 'pill', 'pills', 'mg', 'ml', 'g',
  'strength', 'extra', 'maximum', 'max', 'regular', 'fast', 'rapid', 'extended',
  'release', 'acting', 'relief', 'pain', 'fever', 'cold', 'flu', 'cough',
  'liquid', 'gel', 'cream', 'ointment', 'drops', 'spray', 'patch',
  
  // Dosage and instruction words
  'take', 'use', 'apply', 'daily', 'twice', 'once', 'morning', 'evening',
  'night', 'bedtime', 'food', 'empty', 'stomach', 'water', 'swallow',
  'chew', 'dissolve', 'sublingual', 'topical', 'oral', 'external',
  
  // Warning and label words
  'warning', 'caution', 'danger', 'keep', 'away', 'children', 'reach',
  'store', 'room', 'temperature', 'refrigerate', 'expire', 'expiry',
  'date', 'lot', 'batch', 'manufactured', 'distributed', 'company',
  
  // Common OCR artifacts
  'tli', 'essie', 'iii', 'lll', 'ooo', 'www', 'mmm', 'nnn',
  
  // Numbers and single characters
  '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
  'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
  'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
]);

// Known medicine name patterns that should be preserved
const MEDICINE_PATTERNS = [
  /^[a-z]+ol$/i,        // ends with 'ol' (like tylenol, panadol)
  /^[a-z]+in$/i,        // ends with 'in' (like aspirin, penicillin)
  /^[a-z]+ine$/i,       // ends with 'ine' (like morphine, codeine)
  /^[a-z]+mycin$/i,     // ends with 'mycin' (antibiotics)
  /^[a-z]+cillin$/i,    // ends with 'cillin' (penicillins)
  /^[a-z]+phen$/i,      // ends with 'phen' (like acetaminophen)
];

/**
 * Clean and filter keywords from OCR results
 */
export function filterOCRKeywords(keywords: string[]): string[] {
  if (!keywords || keywords.length === 0) {
    return [];
  }

  const filtered = keywords
    .map(keyword => cleanKeyword(keyword))
    .filter(keyword => isValidMedicineKeyword(keyword))
    .filter((keyword, index, array) => array.indexOf(keyword) === index) // Remove duplicates
    .sort((a, b) => {
      // Prioritize longer, more specific terms
      if (a.length !== b.length) {
        return b.length - a.length;
      }
      // Then prioritize terms that match medicine patterns
      const aMatches = MEDICINE_PATTERNS.some(pattern => pattern.test(a));
      const bMatches = MEDICINE_PATTERNS.some(pattern => pattern.test(b));
      if (aMatches && !bMatches) return -1;
      if (!aMatches && bMatches) return 1;
      return 0;
    })
    .slice(0, 5); // Limit to top 5 keywords

  console.log(`🔍 Filtered keywords: ${keywords.join(', ')} → ${filtered.join(', ')}`);
  return filtered;
}

/**
 * Clean individual keyword
 */
function cleanKeyword(keyword: string): string {
  if (!keyword || typeof keyword !== 'string') {
    return '';
  }

  return keyword
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]/g, '') // Remove special characters
    .replace(/\d+/g, '') // Remove numbers
    .trim();
}

/**
 * Check if a keyword is potentially a valid medicine name
 */
function isValidMedicineKeyword(keyword: string): boolean {
  if (!keyword || typeof keyword !== 'string') {
    return false;
  }

  const cleaned = keyword.toLowerCase().trim();

  // Must be at least 3 characters long
  if (cleaned.length < 3) {
    return false;
  }

  // Must not be longer than 20 characters (reasonable medicine name length)
  if (cleaned.length > 20) {
    return false;
  }

  // Must not be a stop word
  if (STOP_WORDS.has(cleaned)) {
    return false;
  }

  // Must contain at least one vowel (basic word validation)
  if (!/[aeiou]/.test(cleaned)) {
    return false;
  }

  // Must not be all the same character (OCR artifact)
  if (/^(.)\1+$/.test(cleaned)) {
    return false;
  }

  // Prefer terms that match known medicine patterns
  const matchesPattern = MEDICINE_PATTERNS.some(pattern => pattern.test(cleaned));
  
  // If it matches a medicine pattern, it's definitely valid
  if (matchesPattern) {
    return true;
  }

  // For other terms, apply stricter validation
  // Must be at least 4 characters if it doesn't match a pattern
  if (cleaned.length < 4) {
    return false;
  }

  return true;
}

/**
 * Score keywords by their likelihood of being medicine names
 */
export function scoreKeywords(keywords: string[]): Array<{keyword: string, score: number}> {
  return keywords.map(keyword => ({
    keyword,
    score: calculateMedicineScore(keyword)
  })).sort((a, b) => b.score - a.score);
}

/**
 * Calculate a score for how likely a keyword is to be a medicine name
 */
function calculateMedicineScore(keyword: string): number {
  if (!keyword) return 0;

  const cleaned = keyword.toLowerCase().trim();
  let score = 0;

  // Base score for length (sweet spot is 6-12 characters)
  if (cleaned.length >= 6 && cleaned.length <= 12) {
    score += 30;
  } else if (cleaned.length >= 4 && cleaned.length <= 15) {
    score += 20;
  } else if (cleaned.length >= 3) {
    score += 10;
  }

  // Bonus for medicine patterns
  MEDICINE_PATTERNS.forEach(pattern => {
    if (pattern.test(cleaned)) {
      score += 40;
    }
  });

  // Bonus for containing common medicine syllables
  const medicineSyllables = ['phen', 'mycin', 'cillin', 'zole', 'pril', 'sartan', 'statin'];
  medicineSyllables.forEach(syllable => {
    if (cleaned.includes(syllable)) {
      score += 25;
    }
  });

  // Penalty for stop words
  if (STOP_WORDS.has(cleaned)) {
    score -= 50;
  }

  // Penalty for very short or very long terms
  if (cleaned.length < 3 || cleaned.length > 20) {
    score -= 30;
  }

  return Math.max(0, score);
}
